cd ~/IsaacLab

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/train.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON>ube-Franka-v0 \
# --num_envs 2048 \
# --headless

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/train.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON>-v0 \
# --num_envs 2048 \
# --headless

./isaaclab.sh -p scripts/reinforcement_learning/skrl/train.py \
--task <PERSON>-Lift-Cube-ZLZK-v0 \
--num_envs 4096 \
--headless